<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <KeyBindingCategoryDef>
    <defName>Multiplayer</defName>
    <label>multiplayer</label>
    <description>Controls used in multiplayer.</description>
    <isGameUniversal>true</isGameUniversal>
    <checkForConflicts>
      <li>Development</li>
      <li>Game</li>
      <li>SelectionMisc</li>
      <li>MainTabs</li>
    </checkForConflicts>
  </KeyBindingCategoryDef>

  <KeyBindingDef Name="MultiplayerKeyBinding" Abstract="true">
    <category>Multiplayer</category>
  </KeyBindingDef>

  <KeyBindingDef ParentName="MultiplayerKeyBinding">
    <defName>MpToggleChat</defName>
    <label>toggle chat</label>
    <defaultKeyCodeA>Equals</defaultKeyCodeA>
  </KeyBindingDef>
  
  <KeyBindingDef ParentName="MultiplayerKeyBinding">
    <defName>MpPingKey</defName>
    <label>ping location</label>
    <defaultKeyCodeA>Keypad0</defaultKeyCodeA>
  </KeyBindingDef>

</Defs>