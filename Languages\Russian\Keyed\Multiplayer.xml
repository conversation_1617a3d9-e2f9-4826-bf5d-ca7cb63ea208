<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>

  <!-- Main menu -->
  <MpMultiplayerButton>Мультиплеер</MpMultiplayerButton>
  <MpLoadingError1>Ошибка во время загрузки мода Multiplayer.</MpLoadingError1>
  <MpLoadingError2>Это может вызвать десинхронизацию при игре.</MpLoadingError2>
  <MpLoadingError3>Скорее всего, это связанно с тем, что версия мода Multiplayer не совместима с вашей версией Rimworld.</MpLoadingError3>
  <MpLoadingError4>Вы можете попробовать обновить/откатить версию Multiplayer или Rimworld.</MpLoadingError4>
  <MpLoadingError5>Обратите внимание, что игра могла обновится совсем недавно и новая версия мода Multiplayer может быть ещё не обновлена.</MpLoadingError5>

  <!-- Ingame -->
  <MpSimulating>Симулирование мира</MpSimulating>
  <MpSimulatingServer>Симулирование мира сервером</MpSimulatingServer>
  <MpLoading>Загрузка</MpLoading>
  <MpSaving>Сохранение</MpSaving>
  <MpCreatingJoinPoint>Создание точки подключения</MpCreatingJoinPoint> <!-- join-point = точка подключения -->
  <MpServerCloseConfirmationNoTime>Вы уверены, что хотите выключить сервер?</MpServerCloseConfirmationNoTime>
  <MpServerCloseConfirmationTime>Вы уверены, что хотите выключить сервер? Последние сохранение было {0} назад.</MpServerCloseConfirmationTime>
  <MpTraderLeavesIn>Уходит через {0}</MpTraderLeavesIn>
  <MpNotAvailable>Не доступно в мультиплеере.</MpNotAvailable>
  <MpCallingFactionNotAvailable>На текущий момент вызов других фракций не доступен в мультиплеере.</MpCallingFactionNotAvailable> <!-- На момент перевода не используется -->

  <!-- Ticks behind indicator -->
  <MpTicksBehind>Вы в {0} тиках позади.</MpTicksBehind>
  <MpLowerGameSpeed>Рекомендуется снизить скорость игры.</MpLowerGameSpeed>
  <MpForceCatchUp>Нажмите чтобы перейти в режим догоняющего.</MpForceCatchUp>

  <!-- Disconnected -->
  <MpWrongProtocol>Неверная версия протокола</MpWrongProtocol>
  <MpWrongMultiplayerVersionInfo>Сервер использует Multiplayer {0} (протокол {1}). Ваша версия: {2}</MpWrongMultiplayerVersionInfo>
  <MpWrongVersionUpdateInfo>Обновите мод (Переподпишитесь если используете Steam).</MpWrongVersionUpdateInfo>
  <MpWrongVersionUpdateInfoHost>Хост должен обновить мод (Переподписаться если использует Steam).</MpWrongVersionUpdateInfoHost>
  <MpServerClosed>Сервер выключен</MpServerClosed>
  <MpServerFull>Сервер полон</MpServerFull>
  <MpDisconnectServerStarting>Сервер запускается</MpDisconnectServerStarting>
  <MpKicked>Вы были исключены из игры</MpKicked>
  <MpDisconnected>Отключен</MpDisconnected>
  <MpDisconnectedWithInfo>Отключен ({0})</MpDisconnectedWithInfo>
  <MpSteamTimedOut>Время ожидания соединения истекло</MpSteamTimedOut>
  <MpSteamGenericError>Ошибка подключения</MpSteamGenericError>
  <MpPacketErrorLocal>Ошибка чтения пакетов. Вы можете изучить лог ошибок для дополнительной информации.</MpPacketErrorLocal>
  <MpPacketErrorRemote>Удаленная ошибка чтения пакетов. Хост может изучить лог ошибок для дополнительной информации.</MpPacketErrorRemote>
  <MpConnectionFailed>Подключение не удалось</MpConnectionFailed>
  <MpConnectionFailedWithInfo>Подключение не удалось ({0})</MpConnectionFailedWithInfo>
  <MpInvalidUsernameAlreadyPlaying>Ваше имя пользователя уже используется</MpInvalidUsernameAlreadyPlaying>
  <MpInvalidUsernameChars>Ваше имя пользователя содержит недопустимые символы</MpInvalidUsernameChars>
  <MpInvalidUsernameLength>Ваше имя пользователя имеет недопустимую длину</MpInvalidUsernameLength>
  <MpChangeUsernameInfo>Вы можете изменить своё имя в настройках мода.</MpChangeUsernameInfo>
  <MpConnectAsUsername>Подключится как {0}</MpConnectAsUsername>

  <!-- Chat -->
  <MpChatButton>Чат</MpChatButton>
  <MpChatHotkeyInfo>Горячая клавиша чата:</MpChatHotkeyInfo>
  <MpPlayerConnected>{0} подключился</MpPlayerConnected>
  <MpPlayerDisconnected>{0} вышел</MpPlayerDisconnected>
  <MpChatPlayers>Игроки ({0}):</MpChatPlayers>
  <MpChatSend>⏎</MpChatSend> <!-- длинна строки макс 6 -->
    <!-- Need testing Не уверен, как выглядит это окн -->
  <MpSteamAcceptTitle>Принять игроков из Steam</MpSteamAcceptTitle>
  <MpSteamAcceptDesc>Нажмите чтобы принять</MpSteamAcceptDesc>
  <MpSteamAccepted>Игрок был принят</MpSteamAccepted>

  <!-- Ingame ESC screen -->
  <MpHostServer>Создать сервер</MpHostServer>
  <MpConvertToSp>Конвертировать в одиночную игру</MpConvertToSp>
  <MpConvertToSpWarnHost>Вы уверены, что хотите конвертировать текущую игру в одиночную игру? Сервер будет выключен.</MpConvertToSpWarnHost>
  <MpConvertToSpWarn>Вы уверены, что хотите конвертировать текущую игру в одиночную игру? Вы покинете игру.</MpConvertToSpWarn>
  <MpConvertingToSp>Конвертирование</MpConvertingToSp>
  <MpSaveGameAs>Сохранить игру как</MpSaveGameAs>
  <MpWillOverwrite>Существующий файл будет перезаписан</MpWillOverwrite>
  <MpGameSaved>Игра сохранена как {0}</MpGameSaved>
  <MpGameSaveFailed>Ошибка сохранения игры. Вы можете изучить лог ошибок для дополнительной информации.</MpGameSaveFailed>

  <!-- Settings -->
  <MpUsernameSetting>Имя пользователя</MpUsernameSetting> <!-- длинна строки макс 16 -->
  <MpShowPlayerCursors>Показывать курсоры игроков</MpShowPlayerCursors>
  <MpAutoAcceptSteam>Авто-принятие игроков Steam</MpAutoAcceptSteam>
  <MpAutoAcceptSteamDesc>Автоматически принимать любые входящие запросы на подключение от игроков в Steam.</MpAutoAcceptSteamDesc>
  <MpTransparentChat>Прозрачное окно чата</MpTransparentChat>
  <MpAutosaveSlots>Слоты автосо-ния</MpAutosaveSlots> <!-- длинна строки макс 16 -->
  <MpAutosaveSlotsDesc>Количество файлов автосохранения.</MpAutosaveSlotsDesc>
  <MpAggressiveTicking>Агрессивные тики</MpAggressiveTicking>
  <MpAggressiveTickingDesc>Всегда пытаться автоматически догонять сервер ценой FPS.</MpAggressiveTickingDesc>
  <MpAppendNameToAutosave>Добавлять имя к автосохранению</MpAppendNameToAutosave>
  <MpShowModCompat>Показывать совместимость модов</MpShowModCompat>
  <MpShowModCompatDesc>Активирует загрузку и отображение рейтинга совместимости для модов на основе отзывов других игроков</MpShowModCompatDesc>
  <MpEnablePingsSetting>Включить метки игроков</MpEnablePingsSetting> <!-- Метки = ping -->
  <MpPingLocButtonSetting>Cоздать метку</MpPingLocButtonSetting> <!-- длинна строки макс ~13 -->
  <MpJumpToPingButtonSetting>Переместится к метке</MpJumpToPingButtonSetting> <!-- длинна строки макс ~13 но может быть перенос -->
  <MpShowMainMenuAnimation>Показывать анимацию в глав. меню</MpShowMainMenuAnimation> <!-- длинна строки макс 32 -->
  
  <!-- Ping alert -->
  <MpAlertPing>Многопользовательская метка</MpAlertPing>
  <MpAlertPingDesc1>Нажмите чтобы переместится к отмеченной локации: {0}</MpAlertPingDesc1>
  <MpAlertPingDesc2>Нажмите с зажатой клавишей Shift чтобы убрать.</MpAlertPingDesc2>

  <!-- Pausing sessions -->
  <MpDialogsButton>Диалог</MpDialogsButton>
  <MpTradingSession>Торговля</MpTradingSession>
  <MpRitualSession>Подготовка ритуала</MpRitualSession>
  <MpTransportLoadingSession>Загрузка транспортных капсул</MpTransportLoadingSession>
  <MpCaravanFormingSession>Формирования каравана</MpCaravanFormingSession>
  <MpCaravanSplittingSession>Разделение каравана</MpCaravanSplittingSession>
  <MpAnotherRitualInProgress>Подготовка к ритуалу уже в процессе.</MpAnotherRitualInProgress>

  <!-- Connecting -->
  <MpConnecting>Подключение</MpConnecting>
  <MpConnectingTo>Подключение к {0}:{1}</MpConnectingTo>
  <MpConnected>Подключен.</MpConnected>
  <MpJoining>Присоединение</MpJoining>
  <MpDownloading>Загрузка ({0}%)</MpDownloading>
  <MpSteamConnectingTo>Подключение к игре созданной {0}</MpSteamConnectingTo>
  <MpSteamConnectingWaiting>Ожидание подтверждения хоста</MpSteamConnectingWaiting>
  <MpWaitingForGameData>Ожидание данных игры</MpWaitingForGameData>
  <MpInvalidAddress>Недопустимый адрес</MpInvalidAddress>

  <!-- Desynced -->
  <MpDesynced>Игра была рассинхронизирована.</MpDesynced>
  <MpTryResync>Попробовать ресинхронизироваться</MpTryResync>
  <MpOpenDesyncFolder>Открыть папку с информацией</MpOpenDesyncFolder>
  <MpDesyncedWaiting>Ожидание данных от хоста</MpDesyncedWaiting>

  <!-- Hosting -->
  <MpHostIngame>Запустить сервер с этой игрой</MpHostIngame>
  <MpHostSavefile>Запустить сервер из сохранения</MpHostSavefile>
  <MpInvalidEndpoint>Недопустимый адрес: {0}</MpInvalidEndpoint>
  <MpInvalidGameName>Недопустимое название игры</MpInvalidGameName>
  <MpGameName>Название игры</MpGameName>
  <MpMaxPlayers>Кол-во игроков</MpMaxPlayers> <!-- длинна строки макс 14  -->
  <MpAutosaveIntervalMinutes>Период автосох-ния(минуты)</MpAutosaveIntervalMinutes>  <!-- длинна строки макс 26  -->
  <MpAutosaveIntervalDays>Период автосох-ния(дни)</MpAutosaveIntervalDays>  <!-- длинна строки макс 26  -->
  <MpAutosaveIntervalDesc1>Интервал между автосохранениями.</MpAutosaveIntervalDesc1>
  <MpAutosaveIntervalDesc2>Нажмите чтобы переключится между игровыми днями, и реальными минутами.</MpAutosaveIntervalDesc2>
  <MpAutosaveIntervalDesc3>Один игровой день на нормальной скорости равен примерно 16.6 минутам (быстрой: 5.5 минут, супер-быстрой: 2.7 минуты)</MpAutosaveIntervalDesc3>
  <MpLanDesc1>Транслировать информацию об игре в локальную сеть.</MpLanDesc1>
  <MpLanDesc2>Ваш LAN адрес: {0}</MpLanDesc2> <!-- Адрес интерфейса который используется при подключении к внешним ресурсам согласно локальной таблице маршуцизации -->
  <MpRunArbiter>Запустить Arbiter</MpRunArbiter>
  <MpArbiterDesc>Копия игры, которая запускается в фоне и помогает в обнаружении/решении проблем с десинхронизацией.</MpArbiterDesc>
  <MpAsyncTime>Async time</MpAsyncTime> <!-- длинна строки макс 13  -->
  <MpAsyncTimeDesc>Индивидуальное течение времени для каждой карты и планеты.</MpAsyncTimeDesc>
  <MpHostingDevMode>Dev mode</MpHostingDevMode> <!-- длинна строки макс 13  -->
  <MpHostingDevModeDesc1>Включает доступ к инструментам режима разработчика RimWorld.</MpHostingDevModeDesc1>
  <MpHostingDevModeDesc2>Обратите внимание, что доступны не все функции инструментов разработчика. Поддержка связанная с неработающими функциями не оказывается.</MpHostingDevModeDesc2> <!-- Перефразировать? => Чуть лучше но все равно еще можно подумать -->
  <MpHostingDevModeHostOnly>Только хост</MpHostingDevModeHostOnly>
  <MpHostingDevModeEveryone>Все</MpHostingDevModeEveryone>
  <MpLogDesyncTraces>Desync traces</MpLogDesyncTraces> <!-- длинна строки макс 13  -->
  <MpLogDesyncTracesDesc1>Дополнительно логировать стеки вызов для отладки десинхронизации.</MpLogDesyncTracesDesc1>
  <MpLogDesyncTracesDesc2>Не работает на 32 битных и ARM (Apple Silicon) процессорах.</MpLogDesyncTracesDesc2>
  <MpExperimentalFeature>Экспериментальная возможность</MpExperimentalFeature>
  <MpSyncConfigs>Синхр. настр.</MpSyncConfigs> <!-- длинна строки макс 13  -->
  <MpSyncConfigsDesc1>Дает возможность скачать настройки ваших модов другими игроками при подключении.</MpSyncConfigsDesc1>
  <MpSyncConfigsDesc2>Включение может уменьшить количество десинхронизаций.</MpSyncConfigsDesc2>
  <MpSyncConfigsDesc3>Обратите внимание, что настройки некоторых модов могут содержать конфиденциальную информацию (Например API токены).</MpSyncConfigsDesc3>
  <MpAutoJoinPoints>Auto join-points</MpAutoJoinPoints> <!-- длинна строки макс 13  -->
  <MpAutoJoinPointsDesc1>Переключает автоматическое создание точек подключения.</MpAutoJoinPointsDesc1>
  <MpAutoJoinPointsDesc2>Создание точек подключения медленнее чем просто сохранение но ускоряет процесс подключения игроков, так как им не требуется симулировать игру.</MpAutoJoinPointsDesc2>
  <MpAutoJoinPointsDesc3>Вы также можете самостоятельно создать точку подключения, написав /joinpoint в чат.</MpAutoJoinPointsDesc3>
  <MpAutoJoinPointsJoin>При подключении</MpAutoJoinPointsJoin>
  <MpAutoJoinPointsDesync>При десинхронизации</MpAutoJoinPointsDesync>
  <MpAutoJoinPointsAutosave>При автосохранении</MpAutoJoinPointsAutosave>

  <!-- Server browser info buttons -->
  <MpWebsiteButton>Веб-сайт</MpWebsiteButton>
  <MpDiscordButton>Discord</MpDiscordButton>
  <MpLinkButtonDesc>Нажмите чтобы открыть:</MpLinkButtonDesc>
  <MpCompatibilityButton>Совместимость модов</MpCompatibilityButton>
  <MpCompatibilityButtonDesc1>Открывает окно с информацией о совместимости с модами.</MpCompatibilityButtonDesc1>
  <MpCompatibilityButtonDesc2>Также доступна в списке модов.</MpCompatibilityButtonDesc2>
  <!-- На момент перевода не реализовано -->
  <MpActiveConfigsButton>Активные настройки</MpActiveConfigsButton>
  <MpActiveConfigsButtonDesc1>Эта копия RimWorld запущенна с настройками из игры: {0}</MpActiveConfigsButtonDesc1>
  <MpActiveConfigsButtonDesc2>Перезапустите игру, чтобы восстановить предыдущие настройки.</MpActiveConfigsButtonDesc2>

  <!-- Server browser tabs -->
  <MpLan>LAN</MpLan>
  <MpDirect>Прямое</MpDirect>
  <MpSteam>Steam</MpSteam>
  <MpHostTab>Создать</MpHostTab>

  <!-- Server browser LAN and Direct tabs -->
  <MpJoinButton>Присоединиться</MpJoinButton> <!-- Steam window -->
  <MpConnectButton>Подключиться</MpConnectButton> <!-- Direct window -->
  <MpLanSearching>Поиск</MpLanSearching> <!-- Lan window -->

  <!-- Server browser Steam tab -->
  <MpNotConnectedToSteam>Не подключен к Steam</MpNotConnectedToSteam>
  <MpNoFriendsPlaying>Нет друзей играющих в RimWorld</MpNoFriendsPlaying>
  <MpPlayingRimWorld>Играет RimWorld</MpPlayingRimWorld>
  <MpNotInMultiplayer>Не в мультиплеере</MpNotInMultiplayer>

  <!-- Save list -->
  <MpMultiplayerSaves>Мультиплеер</MpMultiplayerSaves>
  <MpSingleplayerSaves>Одиночная игра</MpSingleplayerSaves>
  <MpSaveOutdated>(Устарело)</MpSaveOutdated>
  <MpSaveOutdatedDesc>Это сохранение было сделано в RimWorld {0} но у вас запущенна версия {1}.</MpSaveOutdatedDesc>
  <MpHostButton>Создать</MpHostButton>
  <MpWatchReplay>Смотреть</MpWatchReplay> <!-- Need testing Как текст отражает суть-->
  <MpNothingSelected>Не выбрано сохранение</MpNothingSelected>
  <MpNoSaves>Мультиплеер может быть запущен только из существующих сохранений</MpNoSaves>
  <MpSeeModList>Открыть список модов</MpSeeModList>
  <MpOpenSaveFolder>Открыть папку с сохранениями</MpOpenSaveFolder>
  <MpContinueAnyway>Все равно продолжить</MpContinueAnyway>
  <MpFileRenameButton>Переименовать</MpFileRenameButton>
  <MpFileRename>Переименовать файл в</MpFileRename>
  
  <!-- Mismatch screen -->
  <MpDataMismatch>Несовпадение данных</MpDataMismatch>
  <MpConnectAnyway>Все равно подключится</MpConnectAnyway>
  <MpFixAndRestart>Исправить и перезапустить</MpFixAndRestart>
  <MpMismatchQuit>Выйти</MpMismatchQuit>
  <MpMismatchDefsDiff>Данные определений модов отличаются. Они должны совпадать с сервером для подключения.</MpMismatchDefsDiff> <!-- Перефразировать? -->
  <MpRestartNeeded>Для применения изменений: списка модов, файлов, и настроек, игру необходимо перезапустить.</MpRestartNeeded>
  <MpApplyConfigs>Использовать настройки сервера</MpApplyConfigs>
  <MpApplyConfigsTip>Временное использование настроек сервера, ваши настройки будут возвращены при перезапуске игры. Ваши файлы настроек перезаписаны не будут.</MpApplyConfigsTip>
  <MpApplyModList>Использовать список модов сервера</MpApplyModList>
  <MpApplyModListTip>Ваш текущий список модов будет изменён.</MpApplyModListTip>
  <MpMismatchGeneral>Общий</MpMismatchGeneral>
  <MpMismatchModList>Список модов</MpMismatchModList>
  <MpMismatchFiles>Файлы</MpMismatchFiles>
  <MpMismatchConfigs>Настройки</MpMismatchConfigs>
  <MpMismatchServerSide>Сервер</MpMismatchServerSide>
  <MpMismatchClientSide>Ваш клиент</MpMismatchClientSide>
  <MpMismatchSeeTabs>Вы можете найти дополнительную информацию в соответствующих вкладках.</MpMismatchSeeTabs>
  <MpMismatchRwVersion>Версия RimWorld</MpMismatchRwVersion>
  <MpMismatchMpVersion>Версия Multiplayer</MpMismatchMpVersion>
  <MpMismatchRestart>Перезапустить</MpMismatchRestart>
  <MpMismatchRestartDesc>Вы будете повторно подключены после перезагрузки игры.</MpMismatchRestartDesc>
  <MpMismatchBack>Назад</MpMismatchBack>
  <MpMismatchServerMods>Моды сервера</MpMismatchServerMods>
  <MpMismatchLocalMods>Ваши моды</MpMismatchLocalMods>
  <MpMismatchModListRefresh>Обновить</MpMismatchModListRefresh>
  <MpMismatchNotInstalled>Не установлены: {0}</MpMismatchNotInstalled>
  <MpMismatchModListsMismatch>Списки модов отличаются. Игра должна быть перезапущена.</MpMismatchModListsMismatch>
  <MpMismatchWrongOrder>Списки модов совпадают но их порядок отличается. Игра должна быть перезапущена.</MpMismatchWrongOrder>
  <MpMismatchModListsMatch>Списки модов совпадают</MpMismatchModListsMatch>
  <MpMismatchSubscribe>Подписаться на недостающие моды</MpMismatchSubscribe>
  <MpMismatchSubscribeMsg>Подписан на {0} мод(а/ов). Загрузка в фоновом режиме...</MpMismatchSubscribeMsg>
  <MpMismatchSubscribeNoSteam>Нет подключения к Steam. Моды должны быть установлены самостоятельно.</MpMismatchSubscribeNoSteam>
  <MpMismatchSubscribeNoMissing>Нет недостающих модов.</MpMismatchSubscribeNoMissing>
  <MpMismatchSubscribeDesc1>Подписаться на недостающие моды в мастерской Steam. Загрузка будет выполнена в фоне.</MpMismatchSubscribeDesc1>
  <MpMismatchSubscribeDesc2>Не все недостающие моды доступны в мастерской Steam. Остальные должны быть установлены самостоятельно.</MpMismatchSubscribeDesc2>
  <MpMismatchLocalFiles>Ваши файлы</MpMismatchLocalFiles>
  <MpMismatchLocalConfigs>Ваши настройки</MpMismatchLocalConfigs>
  <MpMismatchTreeRefresh>Обновить</MpMismatchTreeRefresh>
  <MpMismatchTreeMissing>Отсутствует</MpMismatchTreeMissing>
  <MpMismatchTreeAdded>Добавлен</MpMismatchTreeAdded>
  <MpMismatchTreeModified>Изменен</MpMismatchTreeModified>
  <MpMismatchFilesInfo>Проблема с различиями в файлах модов должна быть решена самостоятельно или проигнорирована. Она обычно вызвана различиями в версиях модов или файлами которые остались после обновления.</MpMismatchFilesInfo>
  <MpMismatchFilesInfoMods>Вы можете попробовать обновить/переустановить эти моды (или переподписаться если они доступны в Steam).</MpMismatchFilesInfoMods>
  <!-- Need testing проверить как выглядит окно, и как текст соотносится к реальности -->
  <MpMismatchFilesInfoCore>Для решения проблем с Core и дополнениями, удалите их папки и проверьте целостность игровых файлов в Steam (Совершенно безопасно, пользовательские данные находятся в другом месте). Наведите курсор на папку выше, чтобы увидеть их нахождение.</MpMismatchFilesInfoCore>
  <MpMismatchFilesInfoHost>Проблема также может быть на стороне хоста, и ему может быть необходимо повторить шаги выше.</MpMismatchFilesInfoHost>
  <MpMismatchFileShowPath>Зажмите shift чтобы показать путь до файлов.</MpMismatchFileShowPath>
  <MpMismatchFileOpenPath>Нажмите с зажатым shift чтобы перейти в папку.</MpMismatchFileOpenPath>
  <MpMismatchNodeExpand>Нажмите чтобы свернуть/развернуть.</MpMismatchNodeExpand>
  <MpConfigSyncDisabled>Хост отключил синхронизацию настроек.</MpConfigSyncDisabled>
  <MpFilesMatch>Файлы совпадают</MpFilesMatch>
  <MpConfigsMatch>Настройки совпадают</MpConfigsMatch>

  <!-- Mod compatibility window -->
  <MpModCompatButton>Совместимость с Multiplayer</MpModCompatButton>
  <MpModCompatTitle>Совместимость модов с мультиплеером</MpModCompatTitle>
  <MpModCompatLoading>Загрузка</MpModCompatLoading>
  <MpModCompatLoadingFailed>Ошибка загрузки.</MpModCompatLoadingFailed>
  <MpModCompatActiveMods>Активные</MpModCompatActiveMods>
  <MpModCompatInstalledMods>Установленные</MpModCompatInstalledMods>
  <MpModCompatOutdatedMods>Устаревшие</MpModCompatOutdatedMods>
  <MpModCompatHeaderModName>Имя мода</MpModCompatHeaderModName>
  <MpModCompatHeaderStatus>Статус</MpModCompatHeaderStatus>
  <MpModCompatHeaderNotes>Примечание</MpModCompatHeaderNotes>
  <MpModCompatXmlOnlyDesc>Моды состоящие только из XML файлов, без дополнительной логики, всегда совместимы. Обратите внимание, они могут использовать несовместимые моды как зависимости.</MpModCompatXmlOnlyDesc>
  <MpModCompatScore4>Мод совместим</MpModCompatScore4>
  <MpModCompatScore3>Хорошая совместимость. Некоторые незначительные функции не работают или вызывают десинхронизацию.</MpModCompatScore3>
  <MpModCompatScore2>Плохая совместимость. Некоторые основные и важные функции не работают или вызывают десинхронизацию.</MpModCompatScore2>
  <MpModCompatScore1>Не совместим</MpModCompatScore1>
  <MpModCompatScoreUnk>Неизвестно; пожалуйста поделитесь своими находками в канале #mod-report нашего Discord сервера</MpModCompatScoreUnk>
  <MpHideTranslationMods>Скрыть локализационные моды</MpHideTranslationMods>
  <MpHideTranslationModsDesc>Моды реализующие только локализацию всегда совместимы.</MpHideTranslationModsDesc>

</LanguageData>
