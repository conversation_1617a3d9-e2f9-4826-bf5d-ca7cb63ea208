![banner](https://user-images.githubusercontent.com/49448379/134965756-2a30ffd9-2f6c-43d6-a2a4-584252fc2e4b.png)



The RimWorld Multiplayer mod allows users to play full games of Rimworld cooperatively.

## Links
[Steam Workshop ](https://steamcommunity.com/sharedfiles/filedetails/?id=2606448745)|
[ Website ](https://rimworldmultiplayer.com)|
[ Discord ](https://discord.gg/S4bxXpv)|
[ Documentation](https://hackmd.io/@rimworldmultiplayer/docs/)

## Development
[Git Releases ](https://github.com/rwmt/Multiplayer/releases)|
[ Installation ](https://hackmd.io/Gd_gueokTNui_fqzOSG2Tg#Installation)|
[ Hosting ](https://hackmd.io/Gd_gueokTNui_fqzOSG2Tg#Hosting-and-Joining)|
[ FAQ ](https://hackmd.io/Gd_gueokTNui_fqzOSG2Tg#FAQ)|
[ Contributing ](https://github.com/rwmt/Multiplayer/blob/master/CONTRIBUTORS.md)|
[ DEV Wiki](https://hackmd.io/Gd_gueokTNui_fqzOSG2Tg#Dev-MP-Wiki)

Please do all pull requests to the [development](https://github.com/rwmt/Multiplayer/tree/development) branch.

## Donations

If you’re feeling generous these are people who have contributed greatly to the mod’s development and upkeep.

**[Zetrith](https://patreon.com/zetrith)** - Creator, Core, Support\
**[NotFood](https://ko-fi.com/notfood)** - Core, Mod Compatiblity, Compatibility Commissions\
**[Sokyran](https://ko-fi.com/sokyran)** - Core, Mod Compatiblity, Compatibility Commissions\
[Nebual](https://ko-fi.com/Nebual) - Core\
[Thomas107500](https://ko-fi.com/thomas107500) - Mod Compatiblity\
[Luz](https://ko-fi.com/llavorre) - Support, Admin\
[Mistress Mia](https://ko-fi.com/miaamakiir) - Support, Admin\
[Swept](https://ko-fi.com/swept) - Support, Admin, Website

## Notes
Thanks to Pardeike for making [Harmony](https://github.com/pardeike/Harmony) and RevenantX for creating [LiteNetLib](https://github.com/RevenantX/LiteNetLib)
