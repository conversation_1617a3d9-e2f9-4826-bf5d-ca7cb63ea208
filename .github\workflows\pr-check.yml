# Based on workflow from https://github.com/krafs/LevelUp

name: Validate Pull Request

env:
  SLN_PATH: Source/

on:
  pull_request

jobs:
  builds:
    name: Builds
    runs-on: ubuntu-latest
    steps:
    
    - uses: actions/checkout@v3

    - name: Set up .NET
      uses: actions/setup-dotnet@v3.0.3
      with:
        dotnet-version: 8.0.x
      env:
        DOTNET_NOLOGO: true
        DOTNET_CLI_TELEMETRY_OPTOUT: true
        
    - name: Restore dependencies
      run: dotnet restore ${{ env.SLN_PATH }}

    - name: Build
      run: dotnet build ${{ env.SLN_PATH }} --no-restore