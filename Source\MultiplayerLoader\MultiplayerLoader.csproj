﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>12</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <AssemblyName>MultiplayerLoader</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Krafs.Rimworld.Ref" Version="1.6.4489-beta" />
    <PackageReference Include="Krafs.Publicizer" Version="2.2.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Zetrith.Prepatcher" Version="1.2.0" />
    <PackageReference Include="Lib.Harmony" Version="2.3.3" ExcludeAssets="runtime" />
    <ProjectReference Include="..\Common\Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Publicize Include="Assembly-CSharp;0Harmony" />
  </ItemGroup>

  <!-- Ignore any system frameworks and get the system assemblies from Krafs.Rimworld.Ref -->
  <Target Name="RemoveFramework" BeforeTargets="PrepareForBuild" DependsOnTargets="GetReferenceAssemblyPaths">
    <PropertyGroup>
      <TargetFrameworkDirectory></TargetFrameworkDirectory>
    </PropertyGroup>
  </Target>

</Project>
