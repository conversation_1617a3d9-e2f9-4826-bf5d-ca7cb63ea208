using System.Collections.Generic;
using UnityEngine;
using Verse;

namespace Multiplayer.Client
{

    public class PacketLogWindow : Window
    {
        public override Vector2 InitialSize => new(UI.screenWidth / 2f, UI.screenHeight / 2f);

        private List<LogNode> nodes = new();
        private int logHeight;
        private Vector2 scrollPos;
        private bool storeStackTrace;

        public int NodeCount => nodes.Count;

        public PacketLogWindow(bool storeStackTrace)
        {
            doCloseX = true;

            this.storeStackTrace = storeStackTrace;
        }

        public override void DoWindowContents(Rect rect)
        {
            GUI.BeginGroup(rect);

            Text.Font = GameFont.Tiny;
            Rect outRect = new Rect(0f, 0f, rect.width, rect.height - 30f);
            Rect viewRect = new Rect(0f, 0f, rect.width - 16f, logHeight + 10f);

            Widgets.BeginScrollView(outRect, ref scrollPos, viewRect);

            Rect nodeRect = new Rect(0f, 0f, viewRect.width, 20f);
            foreach (LogNode node in nodes)
                Draw(node, 0, ref nodeRect);

            if (Event.current.type == EventType.Layout)
                logHeight = (int)nodeRect.y;

            Widgets.EndScrollView();

            GUI.EndGroup();
        }

        public void Draw(LogNode node, int depth, ref Rect rect)
        {
            string text = node.text;
            if (depth == 0 && text == SyncLogger.RootNodeName)
                text = node.children[0].text;

            rect.x = depth * 15;
            if (node.children.Count > 0)
            {
                Widgets.Label(rect, node.expand ? "[-]" : "[+]");
                rect.x += 15;
            }

            // Taken from Verse.Text.CalcHeight, edited not to remove "XML tags"
            Text.tmpTextGUIContent.text = text;
            rect.height = Text.CurFontStyle.CalcHeight(Text.tmpTextGUIContent, rect.width);

            Widgets.Label(rect, text);
            if (Widgets.ButtonInvisible(rect))
                node.expand = !node.expand;

            rect.y += (int)rect.height;

            if (node.expand)
                foreach (LogNode child in node.children)
                    Draw(child, depth + 1, ref rect);
        }

        public void AddCurrentNode(IHasLogger hasLogger)
        {
            if (storeStackTrace)
                hasLogger.Log.current.children.Add(new LogNode("Stack trace")
                {
                    children =
                    {
                        new LogNode(StackTraceUtility.ExtractStackTrace())
                    }
                });

            nodes.Add(hasLogger.Log.current);
        }
    }

}
