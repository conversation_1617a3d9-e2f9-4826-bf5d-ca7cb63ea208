<?xml version="1.0" encoding="utf-8"?>
<ModMetaData>
  <packageId>rwmt.Multiplayer.TTDG</packageId>
  <name>Multiplayer 1.6</name>
  <supportedVersions>
    <li>1.5</li>
    <li>1.6</li>
  </supportedVersions>
  <author>RimWorld Multiplayer Team</author>
  <url>https://github.com/rwmt/Multiplayer</url>
  <description>&lt;b&gt;Important: This mod should be placed right below Core and expansions in the mod list to work properly!
Requires RimWorld >= 1.5.4104&lt;/b&gt;\n
Multiplayer mod for RimWorld.
    
FAQ - https://hackmd.io/@rimworldmultiplayer/docs/
Discord - https://discord.gg/S4bxXpv
  </description>
  <modDependencies>
    <li>
      <packageId>brrainz.harmony</packageId>
      <displayName>Harmony</displayName>
      <downloadUrl>https://github.com/pardeike/HarmonyRimWorld/releases/</downloadUrl>
      <steamWorkshopUrl>steam://url/CommunityFilePage/2009463077</steamWorkshopUrl>
    </li>
  </modDependencies>
  <loadAfter>
    <li>Ludeon.RimWorld</li>
    <li>Ludeon.RimWorld.Royalty</li>
    <li>Ludeon.RimWorld.Ideology</li>
    <li>Ludeon.RimWorld.Biotech</li>
    <li>brrainz.harmony</li>
  </loadAfter>
  <loadBefore>
    <li>UnlimitedHugs.HugsLib</li>
    <li>OskarPotocki.VanillaFactionsExpanded.Core</li>
  </loadBefore>
</ModMetaData>
