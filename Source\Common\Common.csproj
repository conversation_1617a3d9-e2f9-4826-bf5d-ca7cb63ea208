﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Nullable>enable</Nullable>
    <LangVersion>12</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <RootNamespace>Multiplayer.Common</RootNamespace>
    <AssemblyName>MultiplayerCommon</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Krafs.Rimworld.Ref" Version="1.6.4489-beta" />
    <PackageReference Include="LiteNetLib" Version="*******" />
    <PackageReference Include="Lib.Harmony" Version="2.3.3" ExcludeAssets="runtime" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="RimWorld.MultiplayerAPI" Version="0.5.0" />
  </ItemGroup>

  <!-- Ignore any system frameworks and get the system assemblies from Krafs.Rimworld.Ref -->
  <Target Name="RemoveFramework" BeforeTargets="PrepareForBuild" DependsOnTargets="GetReferenceAssemblyPaths">
    <PropertyGroup>
      <TargetFrameworkDirectory></TargetFrameworkDirectory>
    </PropertyGroup>
  </Target>

</Project>
