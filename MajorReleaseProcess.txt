1. Check mod version in Source/Common/Version.cs
2. Check RW version in About.xml (Multiplayer version is added automatically by workshop_bundler.sh)
3. Check Preview.png
4. Make a branch to freeze a release for old RW version, remember to compile and push assemblies, edit workshop_bundler.sh
5. Run workshop_bundler.sh
6. Github release zip generated by bundler
7. Steam workshop upload folder Multiplayer/ generated by bundler