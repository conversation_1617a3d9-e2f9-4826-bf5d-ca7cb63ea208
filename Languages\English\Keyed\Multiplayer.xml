<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>

  <!-- Main menu -->
  <MpMultiplayerButton>Multiplayer</MpMultiplayerButton>
  <MpLoadingError1>Multiplayer errored during loading.</MpLoadingError1>
  <MpLoadingError2>This might cause games to desync.</MpLoadingError2>
  <MpLoadingError3>This is likely because this version of the mod wan't made for your version of RimWorld.</MpLoadingError3>
  <MpLoadingError4>Update/downgrade Multiplayer or RimWorld.</MpLoadingError4>
  <MpLoadingError5>NOTE: the game could have updated very recently and there might not be a new version of Multiplayer available yet.</MpLoadingError5>

  <!-- Ingame -->
  <MpSimulating>Simulating</MpSimulating>
  <MpSimulatingServer>Simulating the server</MpSimulatingServer>
  <MpLoading>Loading</MpLoading>
  <MpSaving>Saving</MpSaving>
  <MpCreatingJoinPoint>Creating a join-point</MpCreatingJoinPoint>
  <MpServerCloseConfirmationNoTime>Are you sure you want to close the server?</MpServerCloseConfirmationNoTime>
  <MpServerCloseConfirmationTime>Are you sure you want to close the server? Last saved {0} ago.</MpServerCloseConfirmationTime>
  <MpTraderLeavesIn>Leaves in {0}</MpTraderLeavesIn>
  <MpNotAvailable>Not available in multiplayer.</MpNotAvailable>
  <MpCallingFactionNotAvailable>Calling other factions is currently not available in multiplayer.</MpCallingFactionNotAvailable>
  <MpLowestWinsButtonsDesc1>Current time speed votes, lowest wins.</MpLowestWinsButtonsDesc1>
  <MpLowestWinsButtonsDesc2>Shift+Space to reset your vote.</MpLowestWinsButtonsDesc2>
  <MpLowestWinsButtonsDesc3>The host can Shift+Click here to reset all votes.</MpLowestWinsButtonsDesc3>

  <!-- Ticks behind indicator -->
  <MpTicksBehind>You are {0} ticks behind.</MpTicksBehind>
  <MpLowerGameSpeed>Consider lowering the game speed.</MpLowerGameSpeed>
  <MpForceCatchUp>Click to force catch up.</MpForceCatchUp>

  <!-- Disconnected -->
  <MpWrongProtocol>Wrong protocol version</MpWrongProtocol>
  <MpWrongMultiplayerVersionDesc>The server is hosted on Multiplayer {0} (protocol {1}). Your version: {2} (protocol {3})</MpWrongMultiplayerVersionDesc>
  <MpWrongVersionUpdateInfo>Update the mod (resubscribe if on Steam).</MpWrongVersionUpdateInfo>
  <MpWrongVersionUpdateInfoHost>The host should update the mod (resubscribe if on Steam).</MpWrongVersionUpdateInfoHost>
  <MpServerClosed>Server closed</MpServerClosed>
  <MpServerFull>Server is full</MpServerFull>
  <MpDisconnectServerStarting>Server is starting</MpDisconnectServerStarting>
  <MpKicked>You've been kicked</MpKicked>
  <MpDisconnected>Disconnected</MpDisconnected>
  <MpDisconnectedWithInfo>Disconnected ({0})</MpDisconnectedWithInfo>
  <MpSteamTimedOut>Connection timed out</MpSteamTimedOut>
  <MpSteamGenericError>Connection error</MpSteamGenericError>
  <MpPacketErrorLocal>Local packet read error. See the log for more info.</MpPacketErrorLocal>
  <MpPacketErrorRemote>Remote packet read error. The host can view the log for more info.</MpPacketErrorRemote>
  <MpConnectionFailed>Connection failed</MpConnectionFailed>
  <MpConnectionFailedWithInfo>Connection failed ({0})</MpConnectionFailedWithInfo>
  <MpInvalidUsernameAlreadyPlaying>Username already online</MpInvalidUsernameAlreadyPlaying>
  <MpInvalidUsernameChars>Username has invalid characters</MpInvalidUsernameChars>
  <MpInvalidUsernameLength>Username has invalid length</MpInvalidUsernameLength>
  <MpChangeUsernameInfo>You can change your username in the mod settings.</MpChangeUsernameInfo>
  <MpConnectAsUsername>Connect as {0}</MpConnectAsUsername>
  <MpBadGamePassword>Wrong game password</MpBadGamePassword>

  <!-- Chat -->
  <MpChatButton>Chat</MpChatButton>
  <MpChatHotkeyInfo>Chat hotkey:</MpChatHotkeyInfo>
  <MpPlayerConnected>{0} has joined</MpPlayerConnected>
  <MpPlayerDisconnected>{0} has left</MpPlayerDisconnected>
  <MpChatPlayers>Players ({0}):</MpChatPlayers>
  <MpChatSend>Send</MpChatSend>
  <MpSteamAcceptTitle>Accept Steam players</MpSteamAcceptTitle>
  <MpSteamAcceptDesc>Click to accept</MpSteamAcceptDesc>
  <MpSteamAccepted>Player has been accepted</MpSteamAccepted>

  <!-- Ingame ESC screen -->
  <MpHostServer>Host a server</MpHostServer>
  <MpConvertToSp>Convert to singleplayer</MpConvertToSp>
  <MpConvertToSpWarnHost>Are you sure you want to convert to singleplayer? This will close the server.</MpConvertToSpWarnHost>
  <MpConvertToSpWarn>Are you sure you want to convert to singleplayer? You will leave the multiplayer game.</MpConvertToSpWarn>
  <MpConvertingToSp>Converting</MpConvertingToSp>
  <MpSaveGameAs>Save game as</MpSaveGameAs>
  <MpWillOverwrite>File exists, will overwrite</MpWillOverwrite>
  <MpGameSaved>Game saved as {0}</MpGameSaved>
  <MpGameSaveFailed>Failed to save the game. See the log for more info.</MpGameSaveFailed>

  <!-- Settings -->
  <MpUsernameSetting>Username</MpUsernameSetting>
  <MpShowPlayerCursors>Show player cursors</MpShowPlayerCursors>
  <MpAutoAcceptSteam>Auto-accept Steam</MpAutoAcceptSteam>
  <MpAutoAcceptSteamDesc>Automatically accept any incoming Steam connection requests.</MpAutoAcceptSteamDesc>
  <MpTransparentChat>Transparent chat</MpTransparentChat>
  <MpAutosaveSlots>Autosave slots</MpAutosaveSlots>
  <MpAutosaveSlotsDesc>How many autosave files to keep.</MpAutosaveSlotsDesc>
  <MpAggressiveTicking>Aggressive ticking</MpAggressiveTicking>
  <MpAggressiveTickingDesc>Always try to automatically catch up with the server at the cost of FPS.</MpAggressiveTickingDesc>
  <MpAppendNameToAutosave>Append name to autosave</MpAppendNameToAutosave>
  <MpShowModCompat>Show crowd-sourced compat info</MpShowModCompat>
  <MpShowModCompatDesc>Enables downloading and displaying of crowd-sourced mod compatibility info.</MpShowModCompatDesc>
  <MpEnablePingsSetting>Enable location pings</MpEnablePingsSetting>
  <MpPingLocButtonSetting>Ping location button</MpPingLocButtonSetting>
  <MpJumpToPingButtonSetting>Jump to ping button</MpJumpToPingButtonSetting>
  <MpShowMainMenuAnimation>Show main menu animation</MpShowMainMenuAnimation>
  <MpPlayerCursorTransparency>Transparent player cursors</MpPlayerCursorTransparency>
  <MpSettingsPageGeneral>General settings</MpSettingsPageGeneral>
  <MpSettingsPageColor>Player colors</MpSettingsPageColor>
  <MpSettingsPageColorDesc>Select what colors player get when they connect to a game you host. Affects, for example, player cursors.</MpSettingsPageColorDesc>
  <MpResetColors>Reset the colors</MpResetColors>
  
  <!-- Ping alert -->
  <MpAlertPing>Multiplayer ping</MpAlertPing>
  <MpAlertPingDesc1>Click to focus the camera on locations pinged by: {0}</MpAlertPingDesc1>
  <MpAlertPingDesc2>Shift + click to dismiss.</MpAlertPingDesc2>

  <!-- Pausing sessions -->
  <MpDialogsButton>Dialogs</MpDialogsButton>
  <MpTradingSession>Trading session</MpTradingSession>
  <MpRitualSession>Ritual setup session</MpRitualSession>
  <MpTransportLoadingSession>Transport pod loading session</MpTransportLoadingSession>
  <MpCaravanFormingSession>Caravan forming session</MpCaravanFormingSession>
  <MpCaravanSplittingSession>Caravan splitting session</MpCaravanSplittingSession>
  <MpAnotherRitualInProgress>Another ritual setup session is already in progress.</MpAnotherRitualInProgress>
  <MpSwitchToMap>Switch to map</MpSwitchToMap>
  <MpMapPortalSession>Entering {0} session</MpMapPortalSession>

  <!-- Connecting -->
  <MpConnecting>Connecting</MpConnecting>
  <MpConnectingTo>Connecting to {0}:{1}</MpConnectingTo>
  <MpConnected>Connected.</MpConnected>
  <MpJoining>Joining</MpJoining>
  <MpDownloading>Downloading ({0}%)</MpDownloading>
  <MpSteamConnectingTo>Connecting to a game hosted by {0}</MpSteamConnectingTo>
  <MpSteamConnectingWaiting>Waiting for host to accept</MpSteamConnectingWaiting>
  <MpWaitingForGameData>Waiting for game data</MpWaitingForGameData>
  <MpInvalidAddress>Invalid address</MpInvalidAddress>
  <MpGamePassword>Game password</MpGamePassword>

  <!-- Desynced -->
  <MpDesynced>The game state has desynced.</MpDesynced>
  <MpTryResync>Try resync</MpTryResync>
  <MpOpenDesyncFolder>Open info folder</MpOpenDesyncFolder>
  <MpDesyncedWaiting>Waiting for data from the host</MpDesyncedWaiting>

  <!-- Hosting -->
  <MpHostTabConnecting>Connecting</MpHostTabConnecting>
  <MpHostTabGameplay>Gameplay</MpHostTabGameplay>
  <MpHostDirect>Direct</MpHostDirect>
  <MpHostDirectDesc1>Listen for joining players on specified endpoints.</MpHostDirectDesc1>
  <MpHostDirectDesc2>Usually, leaving this field 0.0.0.0 is enough (it means listen on all network interfaces). Note that you'll likely have to do port forwarding.</MpHostDirectDesc2>
  <MpHostDirectDesc3>To listen on multiple endpoints, separate them with ampersands. IPv6 addresses are supported.</MpHostDirectDesc3>
  <MpHostDirectDesc4>When no port is provided, 30502 is used.</MpHostDirectDesc4>
  <MpHostIngame>Host from this game</MpHostIngame>
  <MpHostSavefile>Host from a savefile</MpHostSavefile>
  <MpInvalidEndpoint>Invalid endpoint: {0}</MpInvalidEndpoint>
  <MpInvalidGameName>Invalid game name</MpInvalidGameName>
  <MpInvalidGamePassword>Invalid game password</MpInvalidGamePassword>
  <MpGameName>Game name</MpGameName>
  <MpMaxPlayers>Max players</MpMaxPlayers>
  <MpAutosaves>Autosaves</MpAutosaves>
  <MpAutosavesEvery>Every</MpAutosavesEvery>
  <MpAutosavesMinutes>minutes</MpAutosavesMinutes>
  <MpAutosavesDays>days</MpAutosavesDays>
  <MpAutosavesDesc1>The interval between autosaves.</MpAutosavesDesc1>
  <MpAutosavesDesc2>Click the unit to switch between ingame days and realtime minutes.</MpAutosavesDesc2>
  <MpAutosavesDesc3>A day on normal speed is about 16.6 minutes (fast: 5.5 minutes, superfast: 2.7 minutes)</MpAutosavesDesc3>
  <MpLanDesc1>Broadcast the game to your local network.</MpLanDesc1>
  <MpLanDesc2>Resolved LAN address: {0}</MpLanDesc2>
  <MpRunArbiter>Run Arbiter</MpRunArbiter>
  <MpArbiterDesc>A background game instance which helps with desync detection/solving.</MpArbiterDesc>
  <MpAsyncTime>Async time</MpAsyncTime>
  <MpAsyncTimeDesc>Separate time controls for each map and the planet.</MpAsyncTimeDesc>
  <MpHostingDevMode>Dev mode</MpHostingDevMode>
  <MpHostingDevModeDesc1>Enables access to RimWorld's development mode tools.</MpHostingDevModeDesc1>
  <MpHostingDevModeDesc2>Note: not all dev tools are usable and no support is provided for them.</MpHostingDevModeDesc2>
  <MpHostingDevModeHostOnly>Host only</MpHostingDevModeHostOnly>
  <MpHostingDevModeEveryone>Everyone</MpHostingDevModeEveryone>
  <MpLogDesyncTraces>Desync traces</MpLogDesyncTraces>
  <MpLogDesyncTracesDesc1>Log extra stacktraces for troubleshooting desyncs.</MpLogDesyncTracesDesc1>
  <MpLogDesyncTracesDesc2>Does not work on 32 bit and ARM (Apple Silicon) processors.</MpLogDesyncTracesDesc2>
  <MpExperimentalFeature>Experimental feature</MpExperimentalFeature>
  <MpSyncConfigs>Sync configs</MpSyncConfigs>
  <MpSyncConfigsDescNew1>Toggle whether joining players can download your mod configuration files.</MpSyncConfigsDescNew1>
  <MpSyncConfigsDescNew2>Enabling this can reduce the number of desyncs.</MpSyncConfigsDescNew2>
  <MpSyncConfigsDescNew3>Note that some mods' config files can contain sensitive information (like Twitch API tokens used by streamer mods).</MpSyncConfigsDescNew3>
  <MpAutoJoinPoints>Auto join-points</MpAutoJoinPoints>
  <MpAutoJoinPointsDesc1>Toggle automatic creation of join-points.</MpAutoJoinPointsDesc1>
  <MpAutoJoinPointsDesc2>Join-point creation is slower than saving but it speeds up joining significantly by not requiring players to simulate the game.</MpAutoJoinPointsDesc2>
  <MpAutoJoinPointsDesc3>You can always create a join-point manually by typing /joinpoint in the chat.</MpAutoJoinPointsDesc3>
  <MpAutoJoinPointsJoin>On join</MpAutoJoinPointsJoin>
  <MpAutoJoinPointsDesync>On desync</MpAutoJoinPointsDesync>
  <MpAutoJoinPointsAutosave>On autosave</MpAutoJoinPointsAutosave>
  <MpHostGamePassword>Password</MpHostGamePassword>
  <MpPauseOn>Pause on</MpPauseOn>
  <MpPauseOnJoin>Join</MpPauseOnJoin>
  <MpPauseOnDesync>Desync</MpPauseOnDesync>
  <MpTimeControl>Time control</MpTimeControl>
  <MpTimeControlEveryoneControls>Everyone controls</MpTimeControlEveryoneControls>
  <MpTimeControlLowestWins>Lowest wins</MpTimeControlLowestWins>
  <MpTimeControlHostOnly>Host only</MpTimeControlHostOnly>
  <MpPauseOnLetter>Pause on letter</MpPauseOnLetter>
  <MpPauseOnLetterNever>Never</MpPauseOnLetterNever>
  <MpPauseOnLetterMajorThreat>Major threats</MpPauseOnLetterMajorThreat>
  <MpPauseOnLetterAnyThreat>Any threat</MpPauseOnLetterAnyThreat>
  <MpPauseOnLetterAnyLetter>Any letter</MpPauseOnLetterAnyLetter>

  <!-- Server browser info buttons -->
  <MpWebsiteButton>Website</MpWebsiteButton>
  <MpDiscordButton>Discord</MpDiscordButton>
  <MpLinkButtonDesc>Click to open:</MpLinkButtonDesc>
  <MpCompatibilityButton>Compatibility</MpCompatibilityButton>
  <MpCompatibilityButtonDesc1>Open mod compatibility information window.</MpCompatibilityButtonDesc1>
  <MpCompatibilityButtonDesc2>Also available from the mod list.</MpCompatibilityButtonDesc2>
  <MpActiveConfigsButton>Active configs</MpActiveConfigsButton>
  <MpActiveConfigsButtonDesc1>This RimWorld instance has been started with configuration files from game: {0}</MpActiveConfigsButtonDesc1>
  <MpActiveConfigsButtonDesc2>Restart to restore your previous configs.</MpActiveConfigsButtonDesc2>

  <!-- Server browser tabs -->
  <MpLan>LAN</MpLan>
  <MpDirect>Direct</MpDirect>
  <MpSteam>Steam</MpSteam>
  <MpHostTab>Host</MpHostTab>

  <!-- Server browser LAN and Direct tabs -->
  <MpJoinButton>Join</MpJoinButton>
  <MpConnectButton>Connect</MpConnectButton>
  <MpLanSearching>Searching</MpLanSearching>

  <!-- Server browser Steam tab -->
  <MpNotConnectedToSteam>Not connected to Steam</MpNotConnectedToSteam>
  <MpNoFriendsPlaying>No friends currently playing RimWorld</MpNoFriendsPlaying>
  <MpPlayingRimWorld>Playing RimWorld</MpPlayingRimWorld>
  <MpNotInMultiplayer>Not in multiplayer</MpNotInMultiplayer>

  <!-- Save list -->
  <MpMultiplayerSaves>Multiplayer</MpMultiplayerSaves>
  <MpSingleplayerSaves>Singleplayer</MpSingleplayerSaves>
  <MpSaveOutdated>(Outdated)</MpSaveOutdated>
  <MpSaveOutdatedDesc>This savefile was made with RimWorld {0} but you are running version {1}.</MpSaveOutdatedDesc>
  <MpHostButton>Host</MpHostButton>
  <MpWatchReplay>Watch</MpWatchReplay>
  <MpNothingSelected>Nothing selected</MpNothingSelected>
  <MpNoSaves>Multiplayer can only be played from already existing saves</MpNoSaves>
  <MpSeeModList>See mod list</MpSeeModList>
  <MpOpenSaveFolder>Open containing folder</MpOpenSaveFolder>
  <MpContinueAnyway>Continue anyway</MpContinueAnyway>
  <MpFileRenameButton>Rename</MpFileRenameButton>
  <MpFileRename>Rename file to</MpFileRename>
  
  <!-- Mismatch screen -->
  <MpDataMismatch>Data mismatch</MpDataMismatch>
  <MpConnectAnyway>Connect anyway</MpConnectAnyway>
  <MpFixAndRestart>Fix and restart</MpFixAndRestart>
  <MpMismatchQuit>Quit</MpMismatchQuit>
  <MpMismatchDefsDiff>Mod data definitions differ. They need to match before joining.</MpMismatchDefsDiff>
  <MpRestartNeeded>The game needs to be restarted for mod list, file and config changes to take effect.</MpRestartNeeded>
  <MpApplyConfigs>Apply server's configs</MpApplyConfigs>
  <MpApplyConfigsTip>The application of configs is temporary and lasts until the next game restart. None of your files will be overriden.</MpApplyConfigsTip>
  <MpApplyModList>Apply server's mod list</MpApplyModList>
  <MpApplyModListTip>Your current mod list will get overriden.</MpApplyModListTip>
  <MpMismatchGeneral>General</MpMismatchGeneral>
  <MpMismatchModList>Mod list</MpMismatchModList>
  <MpMismatchFiles>Files</MpMismatchFiles>
  <MpMismatchConfigs>Configs</MpMismatchConfigs>
  <MpMismatchServerSide>Server</MpMismatchServerSide>
  <MpMismatchClientSide>Your client</MpMismatchClientSide>
  <MpMismatchSeeTabs>See the respective tabs for more info.</MpMismatchSeeTabs>
  <MpMismatchRwVersion>RimWorld version</MpMismatchRwVersion>
  <MpMismatchMpVersion>Multiplayer version</MpMismatchMpVersion>
  <MpMismatchRestart>Restart</MpMismatchRestart>
  <MpMismatchRestartDesc>You will be reconnected after the game finishes loading.</MpMismatchRestartDesc>
  <MpMismatchBack>Back</MpMismatchBack>
  <MpMismatchServerMods>Server mods</MpMismatchServerMods>
  <MpMismatchLocalMods>Active mods</MpMismatchLocalMods>
  <MpMismatchModListRefresh>Refresh</MpMismatchModListRefresh>
  <MpMismatchNotInstalled>Not installed: {0}</MpMismatchNotInstalled>
  <MpMismatchModListsMismatch>Mod lists differ. The game needs to be restarted.</MpMismatchModListsMismatch>
  <MpMismatchWrongOrder>Mods match but their order doesn't. The game needs to be restarted.</MpMismatchWrongOrder>
  <MpMismatchModListsMatch>Mod lists match</MpMismatchModListsMatch>
  <MpMismatchSubscribe>Subscribe to missing</MpMismatchSubscribe>
  <MpMismatchSubscribeMsg>Subscribed to {0} mods. Downloading in the background...</MpMismatchSubscribeMsg>
  <MpMismatchSubscribeNoSteam>Not connected to Steam. Missing mods have to be installed manually.</MpMismatchSubscribeNoSteam>
  <MpMismatchSubscribeNoMissing>No mods are missing.</MpMismatchSubscribeNoMissing>
  <MpMismatchSubscribeDesc1>Subscribe to the missing mods on the Steam Workshop. Downloading will happen in the background.</MpMismatchSubscribeDesc1>
  <MpMismatchSubscribeDesc2>Not all of the missing mods are available on Steam. The rest have to be installed manually.</MpMismatchSubscribeDesc2>
  <MpMismatchLocalFiles>Local files</MpMismatchLocalFiles>
  <MpMismatchLocalConfigs>Local configs</MpMismatchLocalConfigs>
  <MpMismatchTreeRefresh>Refresh</MpMismatchTreeRefresh>
  <MpMismatchTreeMissing>Missing</MpMismatchTreeMissing>
  <MpMismatchTreeAdded>Added</MpMismatchTreeAdded>
  <MpMismatchTreeModified>Modified</MpMismatchTreeModified>
  <MpMismatchFilesInfo>Mod file differences need to be resolved manually or ignored. They are usually caused by differing versions of mods or files being left behind after an update.</MpMismatchFilesInfo>
  <MpMismatchFilesInfoMods>You should try to update/reinstall the offending mods (or resubscribe if on Steam).</MpMismatchFilesInfoMods>
  <MpMismatchFilesInfoCore>To resolve issues with Core and expansion files, remove their folders and revalidate game file integrity (safe to do so, user data is stored elsewhere). Hover over the topmost folders to see their paths.</MpMismatchFilesInfoCore>
  <MpMismatchFilesInfoHost>The differences might originate on the host side and they might have to repeat the above steps.</MpMismatchFilesInfoHost>
  <MpMismatchFileShowPath>Hold shift to show the folder path.</MpMismatchFileShowPath>
  <MpMismatchFileOpenPath>Shift + click to open the folder.</MpMismatchFileOpenPath>
  <MpMismatchNodeExpand>Click to expand/collapse.</MpMismatchNodeExpand>
  <MpConfigSyncDisabled>Config sync disabled by the host</MpConfigSyncDisabled>
  <MpFilesMatch>Files match</MpFilesMatch>
  <MpConfigsMatch>Configs match</MpConfigsMatch>

  <!-- Mod compatibility window -->
  <MpModCompatButton>Multiplayer compatibility</MpModCompatButton>
  <MpModCompatTitle>Multiplayer mod compatibility</MpModCompatTitle>
  <MpModCompatLoading>Loading</MpModCompatLoading>
  <MpModCompatLoadingFailed>Loading failed.</MpModCompatLoadingFailed>
  <MpModCompatActiveMods>Active</MpModCompatActiveMods>
  <MpModCompatInstalledMods>Installed</MpModCompatInstalledMods>
  <MpModCompatOutdatedMods>Outdated</MpModCompatOutdatedMods>
  <MpModCompatHeaderModName>Mod name</MpModCompatHeaderModName>
  <MpModCompatHeaderStatus>Status</MpModCompatHeaderStatus>
  <MpModCompatHeaderNotes>Notes</MpModCompatHeaderNotes>
  <MpModCompatXmlOnlyDesc>XML-only mods provide only data and no logic and so are always compatible. Note though that they can have incompatible mods as dependencies.</MpModCompatXmlOnlyDesc>
  <MpModCompatScore4>Everything works</MpModCompatScore4>
  <MpModCompatScore3>Mostly works, some minor features don't work</MpModCompatScore3>
  <MpModCompatScore2>Partially works, but major features don't work</MpModCompatScore2>
  <MpModCompatScore1>Does not work</MpModCompatScore1>
  <MpModCompatScoreUnk>Unknown; please report findings to #mod-report in our Discord</MpModCompatScoreUnk>
  <MpHideTranslationMods>Hide translation mods</MpHideTranslationMods>
  <MpHideTranslationModsDesc>Translation mods are always compatible.</MpHideTranslationModsDesc>

</LanguageData>