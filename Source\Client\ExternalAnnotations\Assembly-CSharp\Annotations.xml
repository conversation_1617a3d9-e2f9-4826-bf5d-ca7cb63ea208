<assembly name="Assembly-CSharp">
  <member name="T:LudeonTK.DebugActionAttribute">
    <attribute ctor="M:JetBrains.Annotations.MeansImplicitUseAttribute.#ctor"/>
  </member>
  <member name="T:LudeonTK.DebugActionYielderAttribute">
    <attribute ctor="M:JetBrains.Annotations.MeansImplicitUseAttribute.#ctor"/>
  </member>
  <member name="T:LudeonTK.DebugOutputAttribute">
    <attribute ctor="M:JetBrains.Annotations.MeansImplicitUseAttribute.#ctor"/>
  </member>
</assembly>
